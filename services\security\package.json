{"name": "@cktmcs/security-manager", "version": "1.0.0", "description": "Security Manager component for user authentication and authorization", "main": "dist/SecurityManager.js", "scripts": {"start": "ts-node src/SecurityManager.ts", "build": "tsc", "test": "jest", "generate-keys": "node scripts/generate-keys.js", "rotate-keys": "node scripts/rotate-keys.js", "fix-auth-keys": "node scripts/fix-auth-keys.js", "test-auth": "node scripts/test-auth.js", "test-client-auth": "node scripts/test-client-auth.js", "test-auth-e2e": "node scripts/test-auth-e2e.js", "deploy-auth": "node scripts/deploy-auth.js"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@cktmcs/shared": "file:../../shared", "axios": "^1.11.0", "bcrypt": "^6.0.0", "body-parser": "^1.19.0", "cors": "^2.8.5", "express": "^4.21.1", "express-rate-limit": "^7.5.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.18.0", "nodemailer": "^6.9.13", "qrcode": "^1.5.4", "speakeasy": "^2.0.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}}