2025-09-09 16:35:33.921 | RSA private key for plugin signing not found (this is normal for most services)
2025-09-09 16:35:33.927 | Loaded RSA public key for plugin verification
2025-09-09 16:35:34.023 | GitHub repositories enabled in configuration
2025-09-09 16:35:34.579 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-09-09 16:35:34.579 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-09-09 16:35:34.579 | Attempting to connect to RabbitMQ host: rabbitmq
2025-09-09 16:35:34.580 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-09-09 16:35:34.583 | Attempting to register with <PERSON> (attempt 1/10)...
2025-09-09 16:35:34.583 | Using Consul URL: consul:8500
2025-09-09 16:35:34.630 | Successfully initialized repository of type: local
2025-09-09 16:35:34.630 | Successfully initialized repository of type: mongo
2025-09-09 16:35:34.631 | Successfully initialized repository of type: librarian-definition
2025-09-09 16:35:34.632 | Successfully initialized repository of type: git
2025-09-09 16:35:34.632 | Initializing GitHub repository with provided credentials
2025-09-09 16:35:34.633 | GitHubRepository: Initialized for cpravetz/s7plugins. Plugins dir: 'plugins'. Default branch from config/env: main
2025-09-09 16:35:34.633 | Successfully initialized repository of type: github
2025-09-09 16:35:34.634 | Refreshing plugin cache...
2025-09-09 16:35:34.634 | Loading plugins from local repository...
2025-09-09 16:35:34.634 | LocalRepo: Loading fresh plugin list
2025-09-09 16:35:34.637 | Refreshing plugin cache...
2025-09-09 16:35:34.637 | Loading plugins from local repository...
2025-09-09 16:35:34.637 | LocalRepo: Waiting for ongoing plugin list load...
2025-09-09 16:35:34.647 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-09-09 16:35:34.663 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-09-09 16:35:34.688 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-09-09 16:35:34.693 | Service CapabilitiesManager registered with Consul
2025-09-09 16:35:34.693 | Successfully registered CapabilitiesManager with Consul
2025-09-09 16:35:34.693 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-09-09 16:35:34.698 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-09-09 16:35:34.701 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-09-09 16:35:34.703 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-09-09 16:35:34.705 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-09-09 16:35:34.706 | CapabilitiesManager registered successfully with PostOffice
2025-09-09 16:35:34.708 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-09-09 16:35:34.709 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/REFLECT/manifest.json
2025-09-09 16:35:34.711 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-09-09 16:35:34.712 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-09-09 16:35:34.718 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-09-09 16:35:34.721 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TRANSFORM/manifest.json
2025-09-09 16:35:34.723 | LocalRepo: Locators count 12
2025-09-09 16:35:34.737 | Loaded 12 plugins from local repository
2025-09-09 16:35:34.737 | Loading plugins from mongo repository...
2025-09-09 16:35:34.743 | Loaded 12 plugins from local repository
2025-09-09 16:35:34.743 | Loading plugins from mongo repository...
2025-09-09 16:35:34.778 | Loaded 0 plugins from mongo repository
2025-09-09 16:35:34.778 | Loading plugins from librarian-definition repository...
2025-09-09 16:35:34.797 | Loaded 0 plugins from librarian-definition repository
2025-09-09 16:35:34.797 | Loading plugins from git repository...
2025-09-09 16:35:35.312 | Loaded 0 plugins from git repository
2025-09-09 16:35:35.312 | Loading plugins from github repository...
2025-09-09 16:35:35.651 | GitHubRepository: pluginsDir 'plugins' not found on branch main. This is normal for new or empty repositories.
2025-09-09 16:35:35.651 | Loaded 0 plugins from github repository
2025-09-09 16:35:35.651 | Plugin cache refreshed. Total plugins: 12
2025-09-09 16:35:35.651 | Registered 10 internal verbs.
2025-09-09 16:35:35.651 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 404. Details: {"message":"Not Found","documentation_url":"https://docs.github.com/rest/repos/contents#get-repository-content","status":"404"}
2025-09-09 16:35:35.651 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:178:31)
2025-09-09 16:35:35.651 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-09-09 16:35:35.651 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:385:30)
2025-09-09 16:35:35.651 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:664:37)
2025-09-09 16:35:35.651 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:498:13)
2025-09-09 16:35:35.651 | PluginRegistry initialized and cache populated.
2025-09-09 16:35:35.654 | PluginRegistry: Registered verbs after cache refresh: [
2025-09-09 16:35:35.654 |   'ACCOMPLISH',        'API_CLIENT',
2025-09-09 16:35:35.654 |   'CHAT',              'RUN_CODE',
2025-09-09 16:35:35.654 |   'DATA_TOOLKIT',      'FILE_OPERATION',
2025-09-09 16:35:35.654 |   'ASK_USER_QUESTION', 'REFLECT',
2025-09-09 16:35:35.654 |   'SCRAPE',            'SEARCH',
2025-09-09 16:35:35.654 |   'TEXT_ANALYSIS',     'TRANSFORM',
2025-09-09 16:35:35.654 |   'THINK',             'GENERATE',
2025-09-09 16:35:35.654 |   'IF_THEN',           'WHILE',
2025-09-09 16:35:35.654 |   'UNTIL',             'SEQUENCE',
2025-09-09 16:35:35.654 |   'TIMEOUT',           'REPEAT',
2025-09-09 16:35:35.654 |   'FOREACH'
2025-09-09 16:35:35.654 | ]
2025-09-09 16:35:35.654 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-09-09 16:35:35.654 |   'plugin-ACCOMPLISH',
2025-09-09 16:35:35.654 |   'plugin-API_CLIENT',
2025-09-09 16:35:35.654 |   'plugin-CHAT',
2025-09-09 16:35:35.654 |   'plugin-CODE_EXECUTOR',
2025-09-09 16:35:35.654 |   'plugin-DATA_TOOLKIT',
2025-09-09 16:35:35.654 |   'plugin-FILE_OPS_PYTHON',
2025-09-09 16:35:35.654 |   'plugin-ASK_USER_QUESTION',
2025-09-09 16:35:35.654 |   'plugin-REFLECT',
2025-09-09 16:35:35.654 |   'plugin-SCRAPE',
2025-09-09 16:35:35.654 |   'plugin-SEARCH_PYTHON',
2025-09-09 16:35:35.654 |   'plugin-TEXT_ANALYSIS',
2025-09-09 16:35:35.654 |   'plugin-TRANSFORM',
2025-09-09 16:35:35.654 |   'internal-THINK',
2025-09-09 16:35:35.654 |   'internal-REFLECT',
2025-09-09 16:35:35.654 |   'internal-GENERATE',
2025-09-09 16:35:35.654 |   'internal-IF_THEN',
2025-09-09 16:35:35.654 |   'internal-WHILE',
2025-09-09 16:35:35.654 |   'internal-UNTIL',
2025-09-09 16:35:35.654 |   'internal-SEQUENCE',
2025-09-09 16:35:35.654 |   'internal-TIMEOUT',
2025-09-09 16:35:35.654 |   'internal-REPEAT',
2025-09-09 16:35:35.654 |   'internal-FOREACH'
2025-09-09 16:35:35.654 | ]
2025-09-09 16:35:35.774 | Loaded 0 plugins from mongo repository
2025-09-09 16:35:35.774 | Loading plugins from librarian-definition repository...
2025-09-09 16:35:35.854 | Loaded 0 plugins from librarian-definition repository
2025-09-09 16:35:35.855 | Loading plugins from git repository...
2025-09-09 16:35:36.308 | Loaded 0 plugins from git repository
2025-09-09 16:35:36.308 | Loading plugins from github repository...
2025-09-09 16:35:36.621 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 404. Details: {"message":"Not Found","documentation_url":"https://docs.github.com/rest/repos/contents#get-repository-content","status":"404"}
2025-09-09 16:35:36.621 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:178:31)
2025-09-09 16:35:36.621 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-09-09 16:35:36.621 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:385:30)
2025-09-09 16:35:36.621 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:664:37)
2025-09-09 16:35:36.621 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:498:13)
2025-09-09 16:35:36.621 |     at async CapabilitiesManager.initialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:73:21)
2025-09-09 16:35:36.621 |     at async tryInitialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:49:17)
2025-09-09 16:35:36.621 | GitHubRepository: pluginsDir 'plugins' not found on branch main. This is normal for new or empty repositories.
2025-09-09 16:35:36.621 | Loaded 0 plugins from github repository
2025-09-09 16:35:36.621 | Plugin cache refreshed. Total plugins: 22
2025-09-09 16:35:36.621 | Registered 10 internal verbs.
2025-09-09 16:35:36.621 | PluginRegistry initialized and cache populated.
2025-09-09 16:35:36.621 | PluginRegistry: Registered verbs after cache refresh: [
2025-09-09 16:35:36.621 |   'ACCOMPLISH',        'API_CLIENT',
2025-09-09 16:35:36.621 |   'CHAT',              'RUN_CODE',
2025-09-09 16:35:36.621 |   'DATA_TOOLKIT',      'FILE_OPERATION',
2025-09-09 16:35:36.621 |   'ASK_USER_QUESTION', 'REFLECT',
2025-09-09 16:35:36.621 |   'SCRAPE',            'SEARCH',
2025-09-09 16:35:36.621 |   'TEXT_ANALYSIS',     'TRANSFORM',
2025-09-09 16:35:36.621 |   'THINK',             'GENERATE',
2025-09-09 16:35:36.621 |   'IF_THEN',           'WHILE',
2025-09-09 16:35:36.621 |   'UNTIL',             'SEQUENCE',
2025-09-09 16:35:36.621 |   'TIMEOUT',           'REPEAT',
2025-09-09 16:35:36.621 |   'FOREACH'
2025-09-09 16:35:36.621 | ]
2025-09-09 16:35:36.622 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-09-09 16:35:36.622 |   'plugin-ACCOMPLISH',
2025-09-09 16:35:36.622 |   'plugin-API_CLIENT',
2025-09-09 16:35:36.622 |   'plugin-CHAT',
2025-09-09 16:35:36.622 |   'plugin-CODE_EXECUTOR',
2025-09-09 16:35:36.622 |   'plugin-DATA_TOOLKIT',
2025-09-09 16:35:36.622 |   'plugin-FILE_OPS_PYTHON',
2025-09-09 16:35:36.622 |   'plugin-ASK_USER_QUESTION',
2025-09-09 16:35:36.622 |   'plugin-REFLECT',
2025-09-09 16:35:36.622 |   'plugin-SCRAPE',
2025-09-09 16:35:36.622 |   'plugin-SEARCH_PYTHON',
2025-09-09 16:35:36.622 |   'plugin-TEXT_ANALYSIS',
2025-09-09 16:35:36.622 |   'plugin-TRANSFORM',
2025-09-09 16:35:36.622 |   'internal-THINK',
2025-09-09 16:35:36.622 |   'internal-REFLECT',
2025-09-09 16:35:36.623 |   'internal-GENERATE',
2025-09-09 16:35:36.623 |   'internal-IF_THEN',
2025-09-09 16:35:36.623 |   'internal-WHILE',
2025-09-09 16:35:36.623 |   'internal-UNTIL',
2025-09-09 16:35:36.623 |   'internal-SEQUENCE',
2025-09-09 16:35:36.623 |   'internal-TIMEOUT',
2025-09-09 16:35:36.623 |   'internal-REPEAT',
2025-09-09 16:35:36.623 |   'internal-FOREACH'
2025-09-09 16:35:36.623 | ]
2025-09-09 16:35:36.623 | [CapabilitiesManager-constructor-b43bfb2b] CapabilitiesManager.initialize: PluginRegistry initialized.
2025-09-09 16:35:36.627 | [CapabilitiesManager-constructor-b43bfb2b] CapabilitiesManager.initialize: ConfigManager initialized.
2025-09-09 16:35:36.627 | [CapabilitiesManager-constructor-b43bfb2b] CapabilitiesManager.initialize: PluginExecutor initialized.
2025-09-09 16:35:36.627 | [CapabilitiesManager-constructor-b43bfb2b] Setting up express server...
2025-09-09 16:35:36.632 | [CapabilitiesManager-constructor-b43bfb2b] CapabilitiesManager server listening on port 5060
2025-09-09 16:35:36.632 | [CapabilitiesManager-constructor-b43bfb2b] CapabilitiesManager server setup complete
2025-09-09 16:35:36.632 | [CapabilitiesManager-constructor-b43bfb2b] CapabilitiesManager.initialize: CapabilitiesManager initialization completed.
2025-09-09 16:35:39.729 | Connected to RabbitMQ
2025-09-09 16:35:39.734 | Channel created successfully
2025-09-09 16:35:39.734 | RabbitMQ channel ready
2025-09-09 16:35:39.791 | Connection test successful - RabbitMQ connection is stable
2025-09-09 16:35:39.791 | Creating queue: capabilitiesmanager-CapabilitiesManager
2025-09-09 16:35:39.800 | Binding queue to exchange: stage7
2025-09-09 16:35:39.808 | Successfully connected to RabbitMQ and set up queues/bindings
2025-09-09 16:35:58.671 | Created ServiceTokenManager for CapabilitiesManager
2025-09-09 16:35:58.675 | Listing plugins from repository type: all
2025-09-09 16:35:58.681 | Found 22 plugins in total from repository type: all
2025-09-09 16:35:58.749 | [c1f7e388-9ba5-404b-a603-736cd0fb9257] CapabilitiesManager.executeActionVerb: Received request for action execution { actionVerb: 'ACCOMPLISH', inputKeys: [ '0', '1' ] }
2025-09-09 16:35:58.749 | Listing plugins from repository type: all
2025-09-09 16:35:58.756 | Found 22 plugins in total from repository type: all
2025-09-09 16:35:58.757 | StructuredError Generated [CapabilitiesManager.executeAccomplishPlugin]: Core ACCOMPLISH plugin execution failed: Cannot read properties of undefined (reading 'type') (Code: CM010_ACCOMPLISH_PLUGIN_EXECUTION_FAILED, Trace: 5a291351-a8c2-4b50-8168-49dc77fa95d9, ID: 7d55929b-7704-4370-a39d-0442d3051f78)
2025-09-09 16:35:58.757 | [c1f7e388-9ba5-404b-a603-736cd0fb9257] CapabilitiesManager.executeActionVerb: Plugin execution error for ACCOMPLISH: undefined
2025-09-09 16:35:59.763 | [577766fa-f703-435c-a98e-c15e71893dd6] CapabilitiesManager.executeActionVerb: Received request for action execution { actionVerb: 'ACCOMPLISH', inputKeys: [ '0', '1' ] }
2025-09-09 16:35:59.764 | Listing plugins from repository type: all
2025-09-09 16:35:59.769 | Found 22 plugins in total from repository type: all
2025-09-09 16:35:59.770 | StructuredError Generated [CapabilitiesManager.executeAccomplishPlugin]: Core ACCOMPLISH plugin execution failed: Cannot read properties of undefined (reading 'type') (Code: CM010_ACCOMPLISH_PLUGIN_EXECUTION_FAILED, Trace: 887aea0e-7b60-4679-8b60-222e9433c510, ID: 6d734065-8afb-4aa0-8a33-a0cf2b559ed7)
2025-09-09 16:35:59.770 | [577766fa-f703-435c-a98e-c15e71893dd6] CapabilitiesManager.executeActionVerb: Plugin execution error for ACCOMPLISH: undefined
2025-09-09 16:36:00.862 | [6e3f7c2b-f799-40cb-8696-a4e990b3989c] CapabilitiesManager.executeActionVerb: Received request for action execution { actionVerb: 'ACCOMPLISH', inputKeys: [ '0', '1' ] }
2025-09-09 16:36:00.863 | Listing plugins from repository type: all
2025-09-09 16:36:00.874 | Found 22 plugins in total from repository type: all
2025-09-09 16:36:00.875 | StructuredError Generated [CapabilitiesManager.executeAccomplishPlugin]: Core ACCOMPLISH plugin execution failed: Cannot read properties of undefined (reading 'type') (Code: CM010_ACCOMPLISH_PLUGIN_EXECUTION_FAILED, Trace: 49e1a6e6-a811-4b92-a492-23929e34bab6, ID: 29bb5e3e-aee4-40b3-943f-d2efe8b3f8e7)
2025-09-09 16:36:00.876 | [6e3f7c2b-f799-40cb-8696-a4e990b3989c] CapabilitiesManager.executeActionVerb: Plugin execution error for ACCOMPLISH: undefined
2025-09-09 16:36:01.881 | [f33b9cb3-d872-4719-a119-e5e5dc068d65] CapabilitiesManager.executeActionVerb: Received request for action execution { actionVerb: 'ACCOMPLISH', inputKeys: [ '0', '1' ] }
2025-09-09 16:36:01.881 | Listing plugins from repository type: all
2025-09-09 16:36:01.891 | Found 22 plugins in total from repository type: all
2025-09-09 16:36:01.892 | StructuredError Generated [CapabilitiesManager.executeAccomplishPlugin]: Core ACCOMPLISH plugin execution failed: Cannot read properties of undefined (reading 'type') (Code: CM010_ACCOMPLISH_PLUGIN_EXECUTION_FAILED, Trace: 562be0a3-9863-47ed-a73b-6d98479ced78, ID: 50a1ae6e-db14-4e73-ba90-f66ad2b1d4d9)
2025-09-09 16:36:01.892 | [f33b9cb3-d872-4719-a119-e5e5dc068d65] CapabilitiesManager.executeActionVerb: Plugin execution error for ACCOMPLISH: undefined
2025-09-09 16:36:02.985 | [e2f0bee6-3add-4795-8a3a-ae46c897e9cc] CapabilitiesManager.executeActionVerb: Received request for action execution { actionVerb: 'ACCOMPLISH', inputKeys: [ '0', '1' ] }
2025-09-09 16:36:02.986 | Listing plugins from repository type: all
2025-09-09 16:36:02.992 | Found 22 plugins in total from repository type: all
2025-09-09 16:36:02.993 | StructuredError Generated [CapabilitiesManager.executeAccomplishPlugin]: Core ACCOMPLISH plugin execution failed: Cannot read properties of undefined (reading 'type') (Code: CM010_ACCOMPLISH_PLUGIN_EXECUTION_FAILED, Trace: e03a3bc2-45e8-49f2-a5f3-6ddf27e8def7, ID: edf8cad2-373f-46c3-a6f1-7bf5e409272f)
2025-09-09 16:36:02.993 | [e2f0bee6-3add-4795-8a3a-ae46c897e9cc] CapabilitiesManager.executeActionVerb: Plugin execution error for ACCOMPLISH: undefined
2025-09-09 16:36:03.996 | [535666d7-5594-43f0-91ac-1a763a2193fd] CapabilitiesManager.executeActionVerb: Received request for action execution { actionVerb: 'ACCOMPLISH', inputKeys: [ '0', '1' ] }
2025-09-09 16:36:03.997 | Listing plugins from repository type: all
2025-09-09 16:36:04.002 | Found 22 plugins in total from repository type: all
2025-09-09 16:36:04.003 | StructuredError Generated [CapabilitiesManager.executeAccomplishPlugin]: Core ACCOMPLISH plugin execution failed: Cannot read properties of undefined (reading 'type') (Code: CM010_ACCOMPLISH_PLUGIN_EXECUTION_FAILED, Trace: ef5e51b7-7ac3-4694-b99e-9e3df454a970, ID: 06e750d9-3b16-4457-98c6-06a84f584b87)
2025-09-09 16:36:04.003 | [535666d7-5594-43f0-91ac-1a763a2193fd] CapabilitiesManager.executeActionVerb: Plugin execution error for ACCOMPLISH: undefined