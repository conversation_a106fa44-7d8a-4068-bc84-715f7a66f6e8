{"name": "@cktmcs/errorhandler", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && tsc", "test": "jest"}, "dependencies": {"@cktmcs/shared": "file:../shared", "axios": "^1.11.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "typescript": "5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}}